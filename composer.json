{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-dom": "*", "althinect/filament-spatie-roles-permissions": "^2.2", "amidesfahani/filament-tinyeditor": "^2.1", "barryvdh/laravel-dompdf": "^3.0", "filament/filament": "3.2.*", "gusapi/gusapi": "^6.3", "guzzlehttp/guzzle": "^7.2", "lamoda/gs1-barcode-parser": "^1.1", "laravel/framework": "^10.10", "laravel/horizon": "^5.33", "laravel/nightwatch": "^1.10", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "mongodb/laravel-mongodb": "^4.9", "openpayu/openpayu": "^2.3", "openspout/openspout": "4.28.*", "pxlrbt/filament-excel": "^2.3", "spatie/laravel-permission": "^6.3", "webbingbrasil/filament-copyactions": "^3.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.1", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.28", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0", "squizlabs/php_codesniffer": "^3.10"}, "autoload": {"files": ["app/Helpers/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "XSoft\\JPK\\": "app/Services/XSoft/JPK/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}