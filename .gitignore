/.phpunit.cache
/node_modules
/public/hot
/public/storage
/public/js/amidesfahani
/storage/*.key
/vendor
/backup
_ide_helper.php
.phpstorm.meta.php
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode
/docker/certbot/config/*
/docker/certbot/www/*
/docker/cron/*
/docker/mysql/data
/docker/mysql/backup/*
/docker/nginx/tf2ssl
/docker/nginx/logs
/docker/queue/logs
/docker/redis/data
.qodo
/storage/fonts/roboto_*.ufm
/storage/fonts/roboto_*.ttf
/storage/fonts/roboto_*.ufm.json
/storage/fonts/installed-fonts.json
