<div>
    <?php
    $record = $getState();
    $txt_size = $getExtraAttributes()['text_size'] ?? 'text-sm';
    ?>
    <table class="fi-ta-table w-full table-auto">
        <tbody>
        @if(blank($record))
            <tr class="fi-ta-row">
                <td class="fi-ta-cell text-sm text-center" colspan="2">
                    <span class="fi-ta-cell-label text-sm font-semibold">
                        <PERSON>rak danych
                    </span>
                </td>
            </tr>
        @else
            @if($record->type !== \App\Enums\DocumentTypes::FAUP)
                @if($record->type !== \App\Enums\DocumentTypes::PROF)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        {{__('app.trade_docs._.reverse_charge')}}
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        {{$record->getMeta()->getOption('reverse_charge', false) === true ? 'Tak' : 'Nie'}}
                    </td>
                </tr>
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        {{__('app.trade_docs._.mpp')}}
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        {{$record->getMeta()->getOption('mpp', false) === true ? 'Tak' : 'Nie'}}
                    </td>
                </tr>
                @endif
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Metoda obliczania VAT
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        {{$record->vat_method->label()}}
                    </td>
                </tr>
            @endif
            @if($record->is_final_invoice)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Faktura końcowa
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        Tak
                    </td>
                </tr>
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Faktury częściowe
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        @foreach($record->prepaidInvoices as $partial)
                            <a href="{{$this->getResource()::getUrl('add-item', ['record' => $partial->transaction_id])}}">
                                {{$partial->full_doc_number}}
                            </a>
                            <br>
                        @endforeach
                    </td>
                </tr>
            @endif
            @if($record->type === \App\Enums\DocumentTypes::FVP)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Faktura częściowa
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        Tak
                    </td>
                </tr>
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Faktura końcowa
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        @if($record->final_invoice_trade_doc_id)
                            <a href="{{$this->getResource()::getUrl('add-item', ['record' => $record->finalInvoice->transaction_id])}}">
                                {{$record->finalInvoice->full_doc_number}}
                            </a>
                            <br>
                        @else
                            brak
                        @endif
                    </td>
                </tr>
            @endif
            @if($record->correctionDocument)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Korekta faktury
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5 text-warning-400">
                        {{$record->correctionDocument->full_doc_number}}
                    </td>
                </tr>
            @endif
            @if($record->correctedDocument)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Dokument korygowany
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5 text-warning-400">
                        {{$record->correctedDocument->full_doc_number}}
                    </td>
                </tr>
            @endif
            @if($record->payment_reminder_enabled)
                <tr class="fi-ta-row">
                    <td class="fi-ta-cell {{$txt_size}} font-semibold py-1.5">
                        Przypomnienie o zapłacie
                    </td>
                    <td class="fi-ta-cell {{$txt_size}} text-right py-1.5">
                        @if($record->payment_reminder_sent_at)
                            Tak ({{$record->payment_reminder_sent_at->format('Y-m-d H:i') }})
                        @else
                            Nie wysłane ({{$record->payment_reminder_days}} dni przed terminem)
                        @endif
                    </td>
                </tr>
            @endif
        @endif
        </tbody>
    </table>
</div>
