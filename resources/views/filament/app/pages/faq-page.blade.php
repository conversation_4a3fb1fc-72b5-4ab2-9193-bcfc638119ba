<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Search and Filter Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="space-y-4">
                <!-- Search Input -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <x-heroicon-o-magnifying-glass class="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="text"
                        wire:model.live.debounce.300ms="search"
                        placeholder="Szukaj w FAQ..."
                        class="block w-full !px-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                    @if($search)
                        <button
                            wire:click="clearSearch"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                            <x-heroicon-o-x-mark class="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        </button>
                    @endif
                </div>

                <!-- Category Filter -->
                <div class="flex flex-wrap gap-2">
                    <button
                        wire:click="filterByCategory('all')"
                        class="px-4 py-2 rounded-lg border font-medium transition-colors {{ $selectedCategory === 'all' ? 'bg-primary-600 text-white border-primary-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50' }}"
                    >
                        Wszystkie
                    </button>

                    @foreach($categories as $category)
                        <button
                            wire:click="filterByCategory('{{ $category }}')"
                            class="px-4 py-2 rounded-lg border font-medium transition-colors {{ $selectedCategory === $category ? 'bg-primary-600 text-white border-primary-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50' }}"
                        >
                            {{ $category }}
                        </button>
                    @endforeach
                </div>
            </div>
        </div>
        <!-- FAQ Content -->
        @if(count($faqs) > 0)
            @foreach($faqs as $category => $faqCategory)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="border-b border-gray-200 px-6 py-4">
                        <h2 class="text-xl font-semibold text-gray-900">{{ $category }}</h2>
                    </div>

                    <div class="divide-y divide-gray-200">
                        @foreach($faqCategory as $faq)
                            <div
                                wire:key="faq-{{ $faq['id'] }}"
                                x-data="{ open: false }"
                                class="px-6 py-4"
                            >
                                <button
                                    @click="open = !open"
                                    class="w-full text-left flex items-center justify-between group"
                                >
                                    <h3 class="text-lg font-medium text-gray-900 group-hover:text-primary-600 transition-colors pr-4">
                                        {{ $faq['question'] }}
                                    </h3>
                                    <x-heroicon-o-chevron-down
                                        x-bind:class="open ? 'rotate-180' : 'rotate-0'"
                                        class="h-5 w-5 text-gray-500 transform transition-transform duration-200 flex-shrink-0"
                                    />
                                </button>

                                <div
                                    x-show="open"
                                    x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                    class="mt-4"
                                >
                                    <div class="prose prose-sm max-w-none text-gray-700 text-base bg-gray-50 rounded-lg p-4">
                                        {!! $faq['answer'] !!}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <x-heroicon-o-question-mark-circle class="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">Brak wyników</h3>
                <p class="text-gray-500">
                    @if($search)
                        Nie znaleziono FAQ pasujących do wyszukiwania "{{ $search }}".
                    @else
                        Brak dostępnych FAQ w wybranej kategorii.
                    @endif
                </p>
                @if($search)
                    <button
                        wire:click="clearSearch"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 transition-colors"
                    >
                        Wyczyść wyszukiwanie
                    </button>
                @endif
            </div>
        @endif
    </div>
</x-filament-panels::page>
