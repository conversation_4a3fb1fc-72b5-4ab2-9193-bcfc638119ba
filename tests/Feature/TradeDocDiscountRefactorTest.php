<?php

namespace Tests\Feature;

use App\Enums\DocumentTypes;
use App\Enums\TradeDocDiscountTypes;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\TradeDocResource\Forms\TradeDocWithItemsForm;
use App\Models\Partner;
use App\Models\TradeDoc;
use App\Models\User;
use App\Services\CalculateTradeDoc;
use Filament\Forms\Form;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TradeDocDiscountRefactorTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and tenant
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function test_discount_calculation_service_works_with_value_discount()
    {
        $itemData = [
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::VALUE->value,
            'discount_value' => 10.00,
            'amount' => 1,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        $this->assertEquals(90.00, $results['discounted_unit_price']);
        $this->assertArrayHasKey('net_value', $results);
        $this->assertArrayHasKey('vat_value', $results);
        $this->assertArrayHasKey('gross_value', $results);
    }

    /** @test */
    public function test_discount_calculation_service_works_with_percent_discount()
    {
        $itemData = [
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::PERCENT->value,
            'discount_value' => 10.0, // 10%
            'amount' => 1,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        $this->assertEquals(90.00, $results['discounted_unit_price']);
    }

    /** @test */
    public function test_discount_calculation_service_works_with_no_discount()
    {
        $itemData = [
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::NO_DISCOUNT->value,
            'discount_value' => 0,
            'amount' => 1,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        $this->assertEquals(100.00, $results['discounted_unit_price']);
    }

    /** @test */
    public function test_form_schema_contains_discount_action()
    {
        $form = new Form();
        $schema = TradeDocWithItemsForm::itemsSection('add', $form);
        
        // Get the repeater schema
        $repeaterSchema = $schema->getChildComponents()[0]; // The repeater component
        $gridSchema = $repeaterSchema->getSchema()[0]; // The grid component
        $gridComponents = $gridSchema->getSchema();
        
        // Check if there's an Actions component in the schema
        $hasDiscountAction = false;
        foreach ($gridComponents as $component) {
            if ($component instanceof \Filament\Forms\Components\Actions) {
                $hasDiscountAction = true;
                break;
            }
        }
        
        $this->assertTrue($hasDiscountAction, 'Discount action should be present in the form schema');
    }

    /** @test */
    public function test_hidden_discount_fields_are_present()
    {
        $form = new Form();
        $schema = TradeDocWithItemsForm::itemsSection('add', $form);
        
        // Get the repeater schema
        $repeaterSchema = $schema->getChildComponents()[0];
        $gridSchema = $repeaterSchema->getSchema()[0];
        $gridComponents = $gridSchema->getSchema();
        
        $hiddenFields = [];
        foreach ($gridComponents as $component) {
            if ($component instanceof \Filament\Forms\Components\Hidden) {
                $hiddenFields[] = $component->getName();
            }
        }
        
        $this->assertContains('discount_type', $hiddenFields);
        $this->assertContains('discount_value', $hiddenFields);
        $this->assertContains('discounted_unit_price', $hiddenFields);
    }
}
