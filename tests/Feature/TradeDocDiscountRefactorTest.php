<?php

namespace Tests\Feature;

use App\Enums\DocumentTypes;
use App\Enums\TradeDocDiscountTypes;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\TradeDocResource\Forms\TradeDocWithItemsForm;
use App\Models\Partner;
use App\Models\TradeDoc;
use App\Models\User;
use App\Services\CalculateTradeDoc;
use Filament\Forms\Form;
use Tests\TestCase;

class TradeDocDiscountRefactorTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
    }

    /** @test */
    public function test_discount_calculation_service_works_with_value_discount()
    {
        $itemData = [
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::VALUE->value,
            'discount_value' => 10.00,
            'amount' => 1,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        $this->assertEquals(90.00, $results['discounted_unit_price']);
        $this->assertArrayHasKey('net_value', $results);
        $this->assertArrayHasKey('vat_value', $results);
        $this->assertArrayHasKey('gross_value', $results);
    }

    /** @test */
    public function test_discount_calculation_service_works_with_percent_discount()
    {
        $itemData = [
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::PERCENT->value,
            'discount_value' => 10.0, // 10%
            'amount' => 1,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        $this->assertEquals(90.00, $results['discounted_unit_price']);
    }

    /** @test */
    public function test_discount_calculation_service_works_with_no_discount()
    {
        $itemData = [
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::NO_DISCOUNT->value,
            'discount_value' => 0,
            'amount' => 1,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        $this->assertEquals(100.00, $results['discounted_unit_price']);
    }

    /** @test */
    public function test_discount_calculation_maintains_data_integrity()
    {
        // Test that discount calculations preserve the original data structure
        $itemData = [
            'label' => 'Test Item',
            'net_unit_price' => 100.00,
            'gross_unit_price' => 123.00,
            'discount_type' => TradeDocDiscountTypes::VALUE->value,
            'discount_value' => 15.00,
            'amount' => 2,
            'vat_rate' => 23,
            'vat_calculate_based_on' => TradeDocVatMethod::BASE_ON_NET,
        ];

        $calculator = CalculateTradeDoc::make($itemData);
        $calculator->setVatMethod(TradeDocVatMethod::BASE_ON_NET);
        $calculator->calculate();

        $results = $calculator->getResults();

        // Verify all required fields are present
        $this->assertArrayHasKey('discount_type', $results);
        $this->assertArrayHasKey('discount_value', $results);
        $this->assertArrayHasKey('discounted_unit_price', $results);
        $this->assertArrayHasKey('net_value', $results);
        $this->assertArrayHasKey('vat_value', $results);
        $this->assertArrayHasKey('gross_value', $results);

        // Verify discount was applied correctly
        $this->assertEquals(85.00, $results['discounted_unit_price']); // 100 - 15
        $this->assertEquals(TradeDocDiscountTypes::VALUE->value, $results['discount_type']);
        $this->assertEquals(15.00, $results['discount_value']);
    }
}
