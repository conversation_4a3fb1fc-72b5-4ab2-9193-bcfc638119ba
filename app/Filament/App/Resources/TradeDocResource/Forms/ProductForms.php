<?php

namespace App\Filament\App\Resources\TradeDocResource\Forms;

use App\Enums\DocumentTypes;
use App\Enums\TradeDocDiscountTypes;
use App\Enums\TradeDocVatMethod;
use App\Enums\VatRates;
use App\Filament\App\Resources\ProductsResource;
use App\Services\CalculateTradeDoc;
use App\Services\Filters;
use Filament\Forms;
use App\Models\Products;
use App\Repositories\ProductsRepository;
use Filament\Forms\Components\Actions\Action as ComponentAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Support\RawJs;
use Livewire\Livewire;

class ProductForms
{
    use \App\Traits\CalculateItemsStandaloneTrait;


    public function getForm(Form $form, $get, $docTpe): array
    {
        $formType = DocumentTypes::tryFrom($docTpe);
        return match ($formType) {
            DocumentTypes::FVS => $this->FVS($form, $get),
            DocumentTypes::FAUP => $this->FAUP($form, $get),
            default => $this->FVS($form, $get),
        };
    }

    public function FAUP(Form $form, $get): array
    {
        $schema = $this->addProductsSource($form, []);
        $schema = $this->addStandardFAUPFields($form, $schema);
        return $schema;
    }

    public function FVS(Form $form, $get): array
    {
        $schema = $this->addProductsSource($form, []);
        $schema = $this->addStandardFields($form, $schema);
        return $schema;
    }

    protected function addProductsSource(Form $form, array $schema): array
    {
        $select = Select::make('product')
            ->dehydrated(false)
            ->searchable()
            ->preload()
            ->live()
            ->afterStateUpdated(function (\Filament\Forms\Set $set, \Filament\Forms\Get $get, $state) use ($form) {
                if (blank($state)) {
                    return;
                }
                /**
                 * @var Products $product
                 */
                $product = Products::find($state);
                $set('label', $product->name);
                $set('unit_type', $product->basic_unit);
                if (blank($product->price_per_unit)) {
                    return;
                }
                $data = $form->getRawState();
                $vatMethod = TradeDocVatMethod::tryFrom((int) ($data['vat_method'] ?? 1));
                if ($data['type'] === DocumentTypes::FAUP->value) {
                    $set('gross_unit_price', $product->getGrossPriceInt() / 100);
                    $set('vat_method', $vatMethod->value);
                    $set('amount', 1);
                    $this->calculate($set, $get(), $vatMethod);
                    return;
                }

                switch ($vatMethod) {
                    case TradeDocVatMethod::BASE_ON_NET:
                        $set('net_unit_price', $product->getNetPriceInt() / 100);
                        $set('vat_label', $product->vat_label);
                        break;
                    case TradeDocVatMethod::BASE_ON_GROSS:
                        $set('gross_unit_price', $product->getGrossPriceInt() / 100);
                        $set('vat_label', $product->vat_label);
                        break;
                }
                $this->calculate($set, $get(), $vatMethod);
            })
            ->options(Products::pluck('name', 'id')->toArray())
            ->createOptionForm(ProductsResource::getCreateModalFormSchema())
            ->createOptionUsing(function (array $data) {
                return ProductsRepository::createFromModalForm($data)?->id;
            })
            ->createOptionAction(function (ComponentAction $action) {
                $action->color('success')
                    ->icon('heroicon-s-document-plus');
                return $action;
            })
            ->inlineLabel(true)
            ->label(__('app.trade_docs.add_item.product_list'));
        $schema[] = Grid::make(1)
            ->schema([
                $select
            ]);

        return $schema;
    }

    protected function addStandardFields(Form $form, array $schema): array
    {
        $default = [
            Forms\Components\Textarea::make('label')
                ->label(__('app.trade_docs.add_item.label'))
                ->required()
                ->columnSpanFull()
                ->maxLength(512),
            Grid::make(4)
                ->schema([
                    Forms\Components\TextInput::make('net_unit_price')
                        ->label(__('app.trade_docs.add_item.net_unit_price'))
                        ->visible(function (Forms\Get $get) use ($form) {
                            return (int)$form->getRawState()['vat_method'] === TradeDocVatMethod::BASE_ON_NET->value;
                        })
                        ->dehydratedWhenHidden(true)
                        ->readOnly()
                        ->required(),

                    Forms\Components\TextInput::make('gross_unit_price')
                        ->default(0.0)
                        ->live(onBlur: true)
                        ->dehydratedWhenHidden(true)
                        ->visible(function (Forms\Get $get) use ($form) {
                            return (int)$form->getRawState()['vat_method'] === TradeDocVatMethod::BASE_ON_GROSS->value;
                        })
                        ->label(__('app.trade_docs.add_item.gross_unit_price')),

                    Forms\Components\TextInput::make('discounted_unit_price')
                        ->default(0.0)
                        ->disabled()
                        ->dehydrated(true)
                        ->label(__('app.trade_docs.add_item.discounted_unit_price')),
                ]),
            Grid::make(6)
                ->schema([
                    Forms\Components\TextInput::make('amount')
                        ->default(1)
                        ->required()
                        ->readOnly()
                        ->numeric()
                        ->label(__('app.trade_docs.add_item.amount')),
                    Forms\Components\TextInput::make('unit_type')
                        ->default('szt.')
                        ->required()
                        ->label(__('app.trade_docs.add_item.unit_type')),
                    Forms\Components\TextInput::make('net_value')
                        ->default(0.0)
                        ->disabled()
                        ->dehydrated(true)
                        ->label(__('app.trade_docs.add_item.net_value')),
                    Select::make('vat_label')
                        ->options(fn() => VatRates::getRatesForSelect())
                        ->selectablePlaceholder(false)
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_label')),
                    Forms\Components\TextInput::make('vat_rate')
                        ->hidden()
                        ->dehydratedWhenHidden(true)
                        ->numeric()
                        ->default(23)
                        ->label(__('app.trade_docs.add_item.vat_rate')),
                    Forms\Components\TextInput::make('vat_value')
                        ->numeric()
                        ->disabled()
                        ->dehydrated(true)
                        ->default(0.0)
                        ->label(__('app.trade_docs.add_item.vat_value')),
                    Forms\Components\TextInput::make('gross_value')
                        ->numeric()
                        ->disabled()
                        ->dehydrated(true)
                        ->default(0.0)
                        ->label(__('app.trade_docs.add_item.gross_value')),
                ])
        ];
        return array_merge($schema, $default);
    }

    protected function addStandardFAUPFields(Form $form, array $schema): array
    {
        $default = [
            Forms\Components\Textarea::make('label')
                ->label(__('app.trade_docs.add_item.label'))
                ->required()
                ->columnSpanFull()
                ->maxLength(512),
            Grid::make(3)
                ->schema([
                    Forms\Components\TextInput::make('gross_unit_price')
                        ->default(0.0)
                        ->required()
                        ->readOnly()
                        ->dehydratedWhenHidden(true)
                        ->label(__('app.trade_docs.add_item.gross_unit_price')),
                    Forms\Components\TextInput::make('gross_value')
                        ->numeric()
                        ->default(0.0)
                        ->readOnly()
                        ->label(__('app.trade_docs.add_item.gross_value')),
                    Forms\Components\TextInput::make('unit_type')
                        ->default('szt.')
                        ->label(__('app.trade_docs.add_item.unit_type')),
                    Forms\Components\TextInput::make('amount')
                        ->hidden()
                        ->dehydratedWhenHidden(true)
                        ->default(1),

                ])
        ];
        return array_merge($schema, $default);
    }
}
