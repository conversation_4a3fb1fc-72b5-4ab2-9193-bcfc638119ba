<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Enums\DocumentTypes as DocType;
use App\Filament\Actions\SendTradeDocumentEmailAction;
use App\Filament\App\Resources\TradeDocResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\DTOTradeDocMeta;
use App\Models\TradeDoc;
use App\Models\TradeDocItem;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\TenantRepository;
use App\Repositories\TradeDocsRepository;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class EditTradeDocWithItems extends EditRecord
{
    use HasBackToListButton;

    protected static string $resource = TradeDocResource::class;

    public string $transaction_id;
    public bool $accepted = false;
    public ?string $previousUrl = null;
    public $total = 0;

    public function mount($record): void
    {
        if (is_string($record)) {
            $this->transaction_id = $record;
            $this->record = TradeDoc::where('transaction_id', $record)->first();
        } else {
            $this->record = TradeDoc::find($record);
            $this->transaction_id = $this->record->transaction_id;
        }

        if (empty($this->record)) {
            abort(404);
        }

        $this->accepted = (bool)$this->record->is_accepted;

        $this->total = $this->record->gross;

        $this->fillForm();
    }

    public function getHeading(): string|Htmlable
    {
        $title = $this->record->type->label() . ' ' . $this->record->full_doc_number;

        if ($this->record->has_correction) {
            $title .= '  <small style="color: orangered;">(wystawiona korekta)</small>';
        }

        return new HtmlString($title);
    }

    public function form(Form $form): Form
    {
        return TradeDocResource\Forms\TradeDocWithItemsForm::editFormWithItems($form);
    }

    protected function fillForm(): void
    {
        $data = $this->record->toArray();

        $data['meta'] = $this->record->getMeta()->toArray();

        $metaArray = $this->record->meta->meta ?? [];
        $data['buyerdata'] = $metaArray['buyer_address'] ?? [];

        $items = $this->record->items->map(function (TradeDocItem $item) {
            return [
                'uuid' => $item->uuid,
                'label' => $item->label,
                'net_unit_price' => $item->net_unit_price,
                'gross_unit_price' => $item->gross_unit_price,
                'discount_type' => $item->discount_type,
                'discount_value' => $item->discount_value,
                'discounted_unit_price' => $item->discounted_unit_price,
                'amount' => $item->amount,
                'unit_type' => $item->unit_type,
                'vat_rate' => $item->vat_rate,
                'vat_label' => $item->vat_label,
                'net_value' => $item->net_value,
                'vat_value' => $item->vat_value,
                'gross_value' => $item->gross_value,
            ];
        })->toArray();

        $data['items'] = $items;

        $this->form->fill($data);
    }

    public function handleRecordUpdate(TradeDoc|Model $record, array $data): TradeDoc
    {
        return DB::transaction(function () use ($record, $data) {
            $items = $data['items'] ?? [];
            unset($data['items']);

            $meta = $data['meta'] ?? [];
            unset($data['meta']);

            $record->update(Arr::except($data, 'buyerdata'));

            $metaData = DTOTradeDocMeta::make($record->meta);
            $metaData->setUpBankAccount($meta['bank_account'] ?? 'brak');
            $seller = Arr::except(tenant()?->getAttributes() ?? [], ['id', 'config']);
            $buyer = Arr::except($record->buyer->getAttributes() ?? [], ['id', 'installation']);
            $metaData->setUpIssuer($seller);
            $metaData->setUpSeller($seller);
            $metaData->setUpBuyer($buyer, $data['buyerdata'] ?? []);
            $metaData->setOption('reverse_charge', $meta['options']['reverse_charge'] ?? false);
            $metaData->setOption('mpp', $meta['options']['mpp'] ?? false);
            $metaData->setOption('different_seller', $meta['options']['different_seller'] ?? false);
            $metaData->setNote($meta['note'] ?? null);
            $record->meta->update(['meta' => $metaData]);

            $oldItems = $record->items;

            foreach ($items as $itemData) {
                if (blank($itemData['label']) || blank($itemData['amount'])) {
                    continue;
                }
                $oldItem = $oldItems->firstWhere('uuid', $itemData['uuid']);
                if ($oldItem) {
                    TradeDocsRepository::updateDocItem($record, $oldItem, $itemData);
                } else {
                    TradeDocsRepository::createDocItem($record, $itemData);
                }
                $oldItems = $oldItems->reject(fn ($value) => $value->uuid === $itemData['uuid']);
            }
            if ($oldItems->count() > 0) {
                $oldItems->each(fn ($value) => $value->delete());
            }
            TradeDocsRepository::finalInvoiceProcessShort($record, $data['prepaidInvoices'] ?? [], 'edit');
            TradeDocsRepository::updateDocSummary($record);

            return $record;
        });
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit-with-items', ['record' => $this->getRecord()->transaction_id]);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (!isset($data['items'])) {
            $data['items'] = [];
        }

        return $data;
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('Lista')
                ->button()
                ->icon('heroicon-o-list-bullet')
                ->url(function () {
                    return $this->previousUrl ?? self::getResource()::getUrl('index');
                }),
            Action::make('print_doc')
                ->label('Drukuj')
                ->button()
                ->icon('heroicon-o-printer')
                ->visible(fn () => $this->getRecord()->items()->count() > 0)
                ->url(
                    route('print', [
                        'doctype' => strtolower($this->record->type->getGeneralType()->value),
                        'doc' => $this->record->transaction_id,
                        'output' => 'pdf',
                        'print' => 'true'
                    ]),
                    true
                ),
            SendTradeDocumentEmailAction::make()
                ->visible(fn () => $this->getRecord()->items()->count() > 0 &&
                    TenantRepository::canTenantSendEmails($this->getRecord()->tenant)),
            TradeDocResource\Forms\TradeDocForm::getHeaderPaymentAction()->visible(
                fn () => $this->getRecord()->items()->count() > 0 && !$this->getRecord()->is_paid
            )->label('Oznacz jako opłacony'),
            ActionGroup::make([
                Action::make('legacy_edit')
                    ->label('Edytuj (stary sposób)')
                    ->icon('heroicon-o-pencil-square')
                    ->url(
                        fn(Model $record) => self::getResource()::getUrl('edit', ['record' => $record])
                    ),
                Action::make('issue_duplicate')
                    ->modalHeading('Wystaw duplikat')
                    ->icon('heroicon-o-document-duplicate')
                    ->modalWidth('xs')
                    ->label('Wystaw duplikat')
                    ->form([
                        DatePicker::make('issued_at')
                            ->label('Data duplikatu')
                            ->native(false)
                            ->format('Y-m-d')
                            ->displayFormat('Y-m-d')
                            ->default(now('Europe/Warsaw'))
                            ->required(),
                    ])
                    ->action(function (TradeDoc $record, array $data) {
                        $record->addDuplicate($data['issued_at']);
                        $meta = $record->getMeta();
                        $meta->setOption('duplicate', $data['issued_at']);
                        $variant = TenantRepository::getTenantTradeDocVariant(tenant());
                        $pdf = TradeDocsRepository::getPDFData($record, $variant);
                        $name = Str::of($record->full_doc_number)->replace('/', '_')->title()->snake() . '_duplikat';
                        return response()
                            ->streamDownload(
                                function () use ($pdf) {
                                    echo $pdf->stream();
                                },
                                $name . '.pdf',
                                [
                                    'Content-Type' => 'application/pdf'
                                ],
                            );
                    }),
                Action::make('issue_correction')
                    ->modalHeading('Wystaw korektę')
                    ->icon('heroicon-o-document-check')
                    ->modalWidth(MaxWidth::ExtraLarge)
                    ->label('Wystaw korektę')
                    ->form([
                        \Filament\Forms\Components\Grid::make(2)
                            ->schema([
                                DatePicker::make('issued_at')
                                    ->label('Data korekty')
                                    ->native(false)
                                    ->format('Y-m-d')
                                    ->displayFormat('Y-m-d')
                                    ->default(now('Europe/Warsaw'))
                                    ->required(),
                                Select::make('document_series_id')
                                    ->label('Seria numeracji')
                                    ->options(
                                        fn() =>
                                        DocumentSeriesRepository::getSeriesForDocType(DocType::FVK, true)
                                            ->pluck('name', 'id')
                                    )
                                    ->native(true)
                                    ->selectablePlaceholder(false)
                                    ->default(
                                        fn() =>
                                        DocumentSeriesRepository::getDefaultSeriesForDocType(DocType::FVK)->id
                                    )
                                    ->required(),
                                Textarea::make('reason')
                                    ->label('Powód korekty')
                                    ->columnSpanFull()
                                    ->maxLength(255)
                                    ->required(),
                            ]),

                    ])
                    ->action(function (TradeDoc $record, array $data) {
                        $newRecord = TradeDocsRepository::createInvoiceCorrection($record, $data);
                        return redirect()
                            ->to(self::getResource()::getUrl(
                                'edit-with-items',
                                ['record' => $newRecord->transaction_id]
                            ));
                    })
                    ->hidden(fn($record) => $record->type !== DocType::FVS || $record->has_correction),
            ]),
        ];
    }

    protected function getSaveFormAction(): \Filament\Actions\Action
    {
        return parent::getSaveFormAction()
            ->label('Zapisz zmiany');
    }

    protected function getCancelFormAction(): \Filament\Actions\Action
    {
        return self::getBackToListFormAction();
    }

    public function getRelationManagers(): array
    {
        return [];
    }
}
