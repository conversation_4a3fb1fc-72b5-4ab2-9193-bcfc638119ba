<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Enums\SystemModules;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\TradeDocResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\TradeDoc;
use App\Repositories\TradeDocsRepository;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class CreateTradeDocWithItems extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = TradeDocResource::class;
    public $total = 0;


    public static function getNavigationLabel(): string
    {
        return __('app.trade_docs.navigation.create-sell-label');
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return 'heroicon-o-document-text';
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::INVOICES) ?? false;
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.trade_docs.create.page.heading');
    }

    public function form(Form $form): Form
    {
        return TradeDocResource\Forms\TradeDocWithItemsForm::createFormWithItems($form, $this);
    }

    public function handleRecordCreation(array $data): TradeDoc
    {
        return DB::transaction(function () use ($data) {
            $items = $data['items'] ?? [];
            unset($data['items']);

            $meta = $data['meta'] ?? [];
            unset($data['meta']);

            $prepaidInvoices = [];
            if ($data['is_final_invoice'] ?? false) {
                $prepaidInvoices = $data['prepaidInvoices'] ?? [];
            }
            unset($data['prepaidInvoices']);

            $record = new TradeDoc(Arr::except($data, 'buyerdata'));
            $record->installation = auth()->user()?->installation();
            $record->creator_id = auth()->user()->id;
            $record->issuer_id = tenant()->id;

            if ($record->type === DocumentTypes::FAUP) {
                $record->vat_method = TradeDocVatMethod::BASE_ON_GROSS;
            }

            TradeDocsRepository::createDocNumberOnModel($record);
            TradeDocsRepository::createTransactionOnModel($record);
            $record->save();

            TradeDocsRepository::createMetaOnModel($record, $meta, $data);

            if (filled($prepaidInvoices)) {
                TradeDocsRepository::finalInvoiceProcessShort($record, $prepaidInvoices);
            }

            foreach ($items as $itemData) {
                if (filled($itemData['label']) && filled($itemData['amount'])) {
                    TradeDocsRepository::createDocItem($record, $itemData);
                }
            }

            return $record;
        });
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('edit-with-items', ['record' => $this->getRecord()->transaction_id]);
    }

    protected function getCreateFormAction(): \Filament\Actions\Action
    {
        return parent::getCreateFormAction()
            ->label('Utwórz fakturę');
    }

    protected function getCancelFormAction(): \Filament\Actions\Action
    {
        return self::getBackToListFormAction();
    }

    protected function getCreateAnotherFormAction(): \Filament\Actions\Action
    {
        return parent::getCreateAnotherFormAction()
            ->label('Utwórz i dodaj kolejną');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if (!isset($data['items'])) {
            $data['items'] = [];
        }

        return $data;
    }
}
