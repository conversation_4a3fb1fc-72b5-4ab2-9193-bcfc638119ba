# Unified Invoice Creation - Testing Guide

## Overview
The unified invoice creation form now respects the `vat_method` field and provides different input fields based on the VAT calculation method.

## Key Changes Made

### 1. **VAT Method Awareness**
- Form now shows different price input fields based on `vat_method` selection
- When `vat_method` = "BASE_ON_NET" (1): Shows `net_unit_price` field
- When `vat_method` = "BASE_ON_GROSS" (2): Shows `gross_unit_price` field

### 2. **Proper Calculation Logic**
- Uses the same `CalculateTradeDoc` service and `CalculateItemsTrait` logic as the relation managers
- Calculations start from the appropriate price field based on VAT method:
  - **BASE_ON_NET**: Calculations start from `net_unit_price` and derive gross values
  - **BASE_ON_GROSS**: Calculations start from `gross_unit_price` and derive net values
- Real-time updates when changing VAT method, prices, amounts, or VAT rates
- Handles discounts, VAT rates, and complex calculations exactly like existing functionality

### 3. **Form Reactivity**
- VAT method field is now `live()` - changes immediately affect item forms
- Item price fields are conditionally visible based on VAT method
- All calculations update automatically when any relevant field changes

## Manual Testing Steps

### Test 1: NET-based VAT Method
1. Navigate to `/create` (unified invoice creation)
2. Fill in basic invoice details (buyer, dates, etc.)
3. Set `VAT Method` to "BASE_ON_NET" (should be default)
4. In the items section, verify:
   - ✅ `Net Unit Price` field is visible
   - ❌ `Gross Unit Price` field is hidden
   - Enter net price (e.g., 100.00) - this is the starting point for calculations
   - Enter amount (e.g., 2)
   - Select VAT rate (e.g., 23%)
   - Verify calculations:
     - Net Value = 200.00 (100.00 × 2)
     - VAT Value = 46.00 (200.00 × 23%)
     - Gross Value = 246.00 (200.00 + 46.00)
     - Gross Unit Price = 123.00 (calculated automatically)

### Test 2: GROSS-based VAT Method
1. In the same form, change `VAT Method` to "BASE_ON_GROSS"
2. In the items section, verify:
   - ❌ `Net Unit Price` field is hidden
   - ✅ `Gross Unit Price` field is visible
   - Enter gross price (e.g., 123.00) - this is the starting point for calculations
   - Enter amount (e.g., 2)
   - Select VAT rate (e.g., 23%)
   - Verify calculations:
     - Gross Value = 246.00 (123.00 × 2)
     - VAT Value = 46.00 (calculated from gross using VAT rate)
     - Net Value = 200.00 (246.00 - 46.00)
     - Net Unit Price = 100.00 (calculated automatically)

### Test 3: Dynamic VAT Method Changes
1. Add an item with NET method
2. Change VAT method to GROSS
3. Verify the form updates immediately:
   - Field visibility changes
   - Calculations recalculate automatically
   - Values are preserved where appropriate

### Test 4: Multiple Items with Different Configurations
1. Add multiple items to the invoice
2. Test different VAT rates (0%, 5%, 8%, 23%, zw)
3. Test different amounts and prices
4. Verify all calculations are correct

### Test 5: Form Submission
1. Create a complete invoice with items
2. Submit the form
3. Verify:
   - Invoice is created successfully
   - All items are saved with correct values
   - Redirects to add-item page for further editing if needed

## Expected Behavior

### Field Visibility
- **NET Method**: Only `net_unit_price` visible, `gross_unit_price` hidden but dehydrated
- **GROSS Method**: Only `gross_unit_price` visible, `net_unit_price` hidden but dehydrated

### Calculations
- Uses the same calculation logic as existing relation managers
- Handles discounts, VAT rates, and different calculation methods correctly
- Real-time updates on field changes

### Form State
- VAT method changes immediately affect all item forms
- Form is reactive and updates without page refresh
- All fields maintain their state during VAT method changes

## Files Modified

1. **CreateTradeDocWithItems.php** - New unified creation page
2. **TradeDocForm.php** - Added `createFormWithItems()` method and VAT-aware calculations
3. **TradeDocResource.php** - Updated routing to use unified creation as default

## Backward Compatibility

- Legacy two-step process still available at `/create-legacy`
- Existing relation managers unchanged
- All existing functionality preserved
