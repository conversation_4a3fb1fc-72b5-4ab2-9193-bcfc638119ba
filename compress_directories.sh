#!/bin/bash

# compress_directories.sh
# Script to compress all subdirectories in a directory into separate .tgz archives
# Usage: ./compress_directories.sh [directory_path]
# If no directory path is provided, the current directory will be used

set -euo pipefail

# Function to display usage information
function show_usage() {
    echo "Usage: $0 [directory_path]"
    echo "Compresses all subdirectories in the specified directory into separate .tgz archives"
    echo "If no directory path is provided, the current directory will be used"
}

# Function to check if a directory exists
function check_directory() {
    if [ ! -d "$1" ]; then
        echo "❌ Error: Directory '$1' does not exist."
        exit 1
    fi
}

# Parse command line arguments
TARGET_DIR="."
if [ $# -eq 1 ]; then
    if [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
        show_usage
        exit 0
    else
        TARGET_DIR="$1"
    fi
elif [ $# -gt 1 ]; then
    echo "❌ Error: Too many arguments."
    show_usage
    exit 1
fi

# Check if the target directory exists
check_directory "$TARGET_DIR"

# Get absolute path of the target directory
TARGET_DIR=$(cd "$TARGET_DIR" && pwd)

echo "🔍 Finding subdirectories in: $TARGET_DIR"

# Count subdirectories for progress reporting
TOTAL_DIRS=$(find "$TARGET_DIR" -mindepth 1 -maxdepth 1 -type d | wc -l)
if [ "$TOTAL_DIRS" -eq 0 ]; then
    echo "⚠️ No subdirectories found in $TARGET_DIR"
    exit 0
fi

echo "📂 Found $TOTAL_DIRS subdirectories to compress"

# Create a timestamp for the archive names
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Process each subdirectory
COUNTER=0
find "$TARGET_DIR" -mindepth 1 -maxdepth 1 -type d | while read -r dir; do
    COUNTER=$((COUNTER + 1))
    DIR_NAME=$(basename "$dir")
    ARCHIVE_NAME="${DIR_NAME}_${TIMESTAMP}.tgz"

    echo "📦 [$COUNTER/$TOTAL_DIRS] Compressing: $DIR_NAME -> $ARCHIVE_NAME"

    # Create the archive
    tar -czf "$TARGET_DIR/$ARCHIVE_NAME" -C "$TARGET_DIR" "$DIR_NAME"

    if [ $? -eq 0 ]; then
        echo "✅ Successfully created archive: $ARCHIVE_NAME"
    else
        echo "❌ Failed to create archive for $DIR_NAME"
    fi
done

echo "🎉 Compression completed! All subdirectories have been archived."
