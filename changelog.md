# My Company Control Center (MC3) changelog

## [unreleased] 2025-07-25
### Changed
### Added
### Fixed
### Removed


## [0.37.1] 2025-08-08
### Changed
- nginx version updated to 1.29.0-alpine 

## [0.37.0] 2025-08-08
### Added
- configure nginx to serve dummy ssl for IP-based requests

## [0.36.2] 2025-08-07
### Changed
- close the "pay due" calendar on select (TradeDocs, PurchaseDocs)

## [0.36.1] 2025-08-07
### Fixed
- PurchaseDoc currency rate model type error fix

## [0.36.0] 2025-08-06
### Changed
- Restructured PartnerResource form fields layout and corrected field settings
### Added
- Favicon
- Tenant subscription switch functionality
### Fixed
- User address off on forms (NAT-120)
- Change password session not synced
- PayU payment early step error
- String limit when importing from GUS (NAT-118)
### Removed
- Display time from legal docs when published
- Hide auth record on users list


## [0.35.2] 2025-08-01
### Fixed
- null safe operator when checking permissions in LegalDocumentController


## [0.35.0] 2025-08-01
### Changed
- canceled payment flow
- if no subscription, home URL redirect to manage subscription page
- Restrict editable fields, add button customization, and improve form placeholders in TradeDoc resources
### Added
- FAQ section (admin, User)
- TinyMCE plugin
- red border when invoice item value is 0,
- blocking autocomplete by browser on registration data flow, Middleware logout improved
- add the initial number for document number patterns
### Fixed
- bank account empty keyword fix
### Removed


## [0.34.0] 2025-07-25
### Changed
- Updated printing logic in PrintController with conditional PDF download/stream functionality
- Made specific fields read-only in purchase documents (discounted price, VAT/gross values)
- Improved input filtering and formatting in document forms
- Updated button labels to Polish in PurchaseDoc and TradeDoc resources
- Options panels now opened by default in forms
- Bank data is now required when adding bank accounts
### Added
- Terms and conditions acceptance requirement when subscribing to plans
- Enhanced field validation and restrictions for product addition forms
### Fixed
- Invoice correction removal rules
- Blocked number fields when adding products (brutto, VAT value etc.) - NAT-112
### Removed
- Commented out unused HTML print functionality

## [0.33.0] 2025-07-24
### Changed
- backup script do database prepare before archiving
- removed horizon:snapshot from nightwatch events
- removed the fallback route from nightwatch sampling
### Added
- Payment reminder via email

## [0.32.0] 2025-07-22
### Changed
### Added
- Send invoice via email
### Removed

## [0.31.0] 2025-07-20
### Changed
- Updated queue processing system: migrated from Laravel queue workers to Laravel Horizon
- Modified server commands to support Horizon operations
- Updated Docker configuration for more efficient queue processing
### Added
- Laravel Horizon for advanced queue monitoring and management
- Queue test tools: TestJob and TriggerQueueTestCommand for load testing
- Automatic Horizon snapshots scheduled every 5 minutes
- Health checks integration with Horizon
### Removed

## [0.30.0] 2025-07-19
### Changed
- Changed tenant data export: new directory structure, improved permissions (775,664)
- Refactored tenant archiving: extracted `TenantArchiverService` from `TenantRepository`
- Added encryption and compression options for tenant archiving
- Extended the `RemoveTenants` command with encryption options and archive-only mode

### Added
- Implementation of the account deletion workflow with the `mark_for_delete` function
- Added an account deletion confirmation page to the user panel
- Email notifications after account deletion
- Migration is adding the `mark_for_delete` field to the tenant table
- Script to clean up the tenant export directory

## [0.29.1] 2025-07-17
### Changed
- Remove an incremental option from drug import; 
- introduce Nightwatch query rejection; 
- improve an error message in a drug repository


## [0.29.0] 2025-07-16
### Changed
- removing tenant from the cache when deleted
- connection timeout for importDrugs request (from 30s to 120s)

## [0.28.0] 2025-07-15
### Changed
- Nightwatch configuration
- Docker setup for nightwatch
- Updated logging stack order for ImportDrugs, UpdateCurrencyRatio, and CurrencyRatesExchangeRepository
- Refined validation for company details with full address support and updated URL validation
- Adjusted manufacturer visibility logic to ensure the list is only available for tenants with the Warehouse module enabled

### Added
- Revealable password option in form fields, new validation messages, and updated helper texts
- Layout adjustments to registration forms
- Polish translations updates
- Excluded `Health` command from Nightwatch sampling

### Removed
- User address and account name



## [0.27.0] 2025-07-11
### Added
- Nightwatch installed
### Changed
- logging configuration changed to work with nightwatch
- docker setup changed related to nightwatch


## [0.26.0] 2025-07-09
### Added
- Legal documents

## [0.24.0] 2025-07-07
### Added
- Translations


## [0.23.0] 2025-07-07
### Added
- Upgrade subscription with payment

## [0.22.0] 2025-06-27
### Added
- Weight to plans
- Plan can be only upgraded not downgraded
- Better subscription handling and logic


## [0.20.0] 2025-06-26
### Added
- Daily statistics aggregation to database
- Stats service with facade
- Stats overview widget
- Stats logging
- Clean registrations command, registration validity time in env

## [0.19.0] 2025-06-24
### Fixed
- update user profile fix (Admin)
### Added
- Documentation

## [0.18.0] 2025-06-24
### Added
- Soft deletes to Tenant
- invoice logo warning when tenant logo is not set

## [0.17.0] 2025-06-24
### Changed
### Added
- Subscription management
- Payment management
- Payment providers (PayU)
- Payment webhooks
### Removed
- Direct queue worker commands (replaced with Horizon)


## [0.16.0] 2025-05-30
### Changed
- docker prepared for production
### Added
- image to tenant
- invoice configuration
- invoice templates
### Removed


## [0.15.0] 2025-05-26
### Changed
- docker prepared for production
### Added
### Removed


## [0.14.2] 2025-05-19
### Changed
- composer update (max php 8.2 )
### Added
### Removed


## [0.14.1] 2025-05-19
### Changed
- composer update
### Added
### Removed


## [0.14.0] 2025-05-19
### Changed
### Added
- Charts to user panel
- SIMPLE_CHARTS as a system module
### Removed

## [0.13.2] 2025-05-15
### Changed
- GUS import improved and secured
- TradeDocs select the first trade doc type if only one exists
- Invoice correction is unpaid when created
### Added
### Removed

## [0.13.1] 2025-05-12
### Changed
- docker build updated to 8.3:latest (8.3.21)
- missing image to VCS
### Added
### Removed


## [0.13.0] 2025-05-12
### Changed
- partner CRUD removed the business type, the accounting type
- user Panel uses hash now
- login restriction: must be active, must have the tenant (if app), must be the God (if admin)
- Add error handling and logging for JPK XML generation
- code formatting
### Added
- Registration
### Removed

## [0.12.0] 2025-04-03
### Added
- Forms color themes, layout changed 

## [0.11.0] 2025-03-24
### Added
- Old invoices import
- Tenant export
### Fixed
- Creating new warehouse by admin makes it active by default now

## [0.10.0] 2025-03-18
### Added
- Purchase docs

## [0.9.1] 2025-03-05
### Added
- GUS Api 
### Changed
- New add product quick form
- Partner short name


## [0.9.0] 2025-02-28
### Added
- Visual redesign part 1. Added theme file

## [0.7.0] 2024-09-05
### Added
- Docker local environment

## [0.6.0] 2024-08-30
### Added
- Multi-domain support

## [0.5.0] 2024-07-25
### Added
- Warehouse module
- System modules management
- Versioning
