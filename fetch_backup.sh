#!/bin/bash

set -euo pipefail

# Konfiguracja
REMOTE_USER="devmaster"
REMOTE_HOST="**************"
REMOTE_PORT=6390
REMOTE_PATH="/home/<USER>/html/nativet/storage/app/tenant-export/archive"
REMOTE_PROJECT_PATH="/home/<USER>/html/nativet"
LOCAL_PATH="/var/www/html/nativet.web/backup"
SSH_KEY="/home/<USER>/.ssh/id_rsa"

# Sprawdzenie katalogu lokalnego
mkdir -p "$LOCAL_PATH"

# Pobieranie listy plików .tgz
echo "📥 Pobieram listę plików .tgz ze zdalnego serwera..."
FILES=$(ssh -i "$SSH_KEY" -p $REMOTE_PORT "${REMOTE_USER}@${REMOTE_HOST}" "find '$REMOTE_PATH' -maxdepth 1 -type f -name '*.zip'")

# Pobranie i usunięcie każdego pliku
for FILE in $FILES; do
    FILENAME=$(basename "$FILE")
    echo "➡️  Pobieram plik: $FILENAME"

    scp -i "$SSH_KEY" -P $REMOTE_PORT "${REMOTE_USER}@${REMOTE_HOST}:$FILE" "$LOCAL_PATH/"

    if [ $? -eq 0 ]; then
        echo "🗑️  Kasuję zdalny plik: $FILE"
        ssh -i "$SSH_KEY" -p $REMOTE_PORT "${REMOTE_USER}@${REMOTE_HOST}" "cd '$REMOTE_PROJECT_PATH' && ./server exec app rm -f 'storage/app/tenant-export/archive/$FILENAME'"
    else
        echo "❌ Błąd pobierania pliku $FILENAME – nie usuwam."
    fi
done

echo "✅ Zakończono."
